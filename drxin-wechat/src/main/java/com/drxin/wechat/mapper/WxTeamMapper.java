package com.drxin.wechat.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.drxin.wechat.domain.WxTeam;
import org.apache.ibatis.annotations.Mapper;

/**
 * 微信团队管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Mapper
public interface WxTeamMapper extends BaseMapper<WxTeam> {
    
    /**
     * 根据用户ID查询团队信息
     * 
     * @param userId 用户ID
     * @return 团队信息
     */
    WxTeam selectTeamByUserId(String userId);

    /**
     * 根据团队ID查询团队信息
     * 
     * @param teamId 团队ID
     * @return 团队信息
     */
    WxTeam selectTeamByTeamId(String teamId);
}
