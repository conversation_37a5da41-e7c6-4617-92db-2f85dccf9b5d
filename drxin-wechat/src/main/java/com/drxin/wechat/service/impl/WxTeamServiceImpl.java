package com.drxin.wechat.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.wechat.domain.WxTeam;
import com.drxin.wechat.domain.WxTeamMember;
import com.drxin.wechat.mapper.WxTeamMapper;
import com.drxin.wechat.mapper.WxTeamMemberMapper;
import com.drxin.wechat.service.IWxTeamService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 微信团队管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class WxTeamServiceImpl extends ServiceImpl<WxTeamMapper, WxTeam> implements IWxTeamService {

    @Resource
    private WxTeamMapper wxTeamMapper;

    @Resource
    private WxTeamMemberMapper wxTeamMemberMapper;

    /**
     * 获取我的团队信息
     * 
     * @param userId 用户ID
     * @return 团队信息
     */
    @Override
    public WxTeam getMyTeamInfo(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            return null;
        }
        return wxTeamMapper.selectTeamByUserId(userId);
    }

    /**
     * 获取我的团队成员列表
     * 
     * @param userId 用户ID
     * @return 团队成员列表
     */
    @Override
    public List<WxTeamMember> getMyTeamMembers(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            return null;
        }
        return wxTeamMemberMapper.selectMembersByUserId(userId);
    }
}
